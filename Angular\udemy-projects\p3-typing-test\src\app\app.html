<div class="section-bg d-flex flex-column justify-content-center">
  <div class="container border border-4 border-secondary bg-warning-subtle rounded p-2" style="max-width: 800px">
    <!-- Heading -->
    <h1 class="d-flex justify-content-center">
      <div class="border border-4 border-warning p-2 rounded mt-2"><code
          class="fw-bold text-danger">Typing Challenge</code></div>
    </h1>
    <!--Random Generated Text -->
    <p class="border border-4 p-2 rounded mt-2 bg-light-subtle">
      <ng-container *ngFor="let item of generatedText.split(''); let index = index">
        <span [ngClass]="{
                'text-success fw-bold': userText[index] === item,
                'text-danger fw-bold': userText[index] && userText[index] !== item,
                'text-black': !userText[index]
              }">{{ item }}</span>
      </ng-container>
    </p>


    <br>
    <div class="mb-2">
      <label class="fw-bold">Select Timer: </label>
      <!-- Timer Buttons -->
      <button class="btn btn-outline-dark btn-sm mx-1" (click)="startChallenge(15)">15s</button>
      <button class="btn btn-outline-dark btn-sm mx-1" (click)="startChallenge(30)">30s</button>
      <button class="btn btn-outline-dark btn-sm mx-1" (click)="startChallenge(60)">60s</button>
      <button class="btn btn-outline-dark btn-sm mx-1" (click)="startChallenge(90)">90s</button>
    </div>

    <div *ngIf="timerRunning" class="alert alert-info text-center fw-bold">
      Time Left: {{ timeLeft }}s
    </div>
    <!-- Buttons -->
    <div class="d-flex justify-content-between">
      <button class="btn btn-primary" (click)="generateNewText()">
        Generate New Text <i class="fa-solid fa-rotate-right"></i>
      </button>
      <button class="btn btn-secondary" (click)="this.userText = ''">
        Reset Inserted Text <i class="fa-solid fa-rotate-right"></i>
      </button>
    </div>
    <br>
    <!-- User Input -->
    <div class="">
      <!-- <input class="" type="textarea"   /> -->
      <code><textarea (paste)="false" class="form-control code" style="height: 250px" (input)="onUserInput($event)" [ngModel]="userText"
        placeholder="Start typing here"></textarea></code>

    </div>
    <div *ngIf="reportVisible" class="alert alert-secondary mt-3">
      <h4 class="fw-bold text-success">⏱️ Time's Up! Accuracy Report:</h4>
      <ul class="mb-0">
        <li><strong>Total Typed:</strong> {{ userText.length }}</li>
        <li><strong>Correct Characters:</strong> {{ correctChars }}</li>
        <li><strong>Accuracy:</strong> {{ accuracy }}%</li>
      </ul>
    </div>
    <div *ngIf="reportVisible" class="mt-3">
      <canvas baseChart [data]="barChartData" [options]="barChartOptions" chartType="bar">
      </canvas>
    </div>
    <!-- Timer -->
    @if (userText == generatedText) {
    <div class="container text-center d-flex justify-content-center">
      <h1 class="text-white rounded w-auto h-auto shadow mt-2 p-2 bg-success"> 🎉Success🥳</h1>
    </div>
    }
  </div>
</div>