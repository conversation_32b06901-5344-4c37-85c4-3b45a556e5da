{"name": "p3-typing-test", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng s -o", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:p3-typing-test": "node dist/p3-typing-test/server/server.mjs"}, "private": true, "dependencies": {"@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0", "@angular/ssr": "^20.0.0", "@fortawesome/angular-fontawesome": "^2.0.1", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@types/faker": "^5.5.9", "bootstrap": "^5.3.6", "chart.js": "^4.4.9", "express": "^5.1.0", "faker": "^5.5.3", "fontawesome": "^5.6.3", "lorem-ipsum": "^2.0.8", "ng2-charts": "^8.0.0", "p3-typing-test": "file:", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.0.0", "@angular/cli": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}