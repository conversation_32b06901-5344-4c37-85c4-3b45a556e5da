import { FormsModule } from '@angular/forms';
import { Component, NgModule } from '@angular/core';
import { NgClass, NgF<PERSON>, NgIf } from '@angular/common';
import { LoremIpsum } from 'lorem-ipsum';
import NgChartsModule from 'ng2-charts';
import { ChartConfiguration, ChartOptions } from 'chart.js';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [NgIf, NgClass, NgFor, FormsModule, NgChartsModule],
  templateUrl: './app.html',
  styleUrl: './app.css',
})
export class App {
  protected title = 'p3-typing-test';
  lorem = new LoremIpsum();
  generatedText = '';
  userText = '';
  timeLeft = 0;
  timerRunning = false;
  timerInterval: any;
  reportVisible = false;
  accuracy = 0;
  correctChars = 0;

  // Chart data
  public barChartOptions: ChartOptions<'bar'> = {
    responsive: true,
  };

  public barChartLabels: string[] = [
    'Total Typed',
    'Correct Chars',
    'Accuracy %',
  ];
  public barChartData: ChartConfiguration<'bar'>['data'] = {
    labels: this.barChartLabels,
    datasets: [
      {
        data: [0, 0, 0],
        label: 'Typing Report',
        backgroundColor: ['#ffc107', '#28a745', '#007bff'],
      },
    ],
  };
  ngOnInit() {
    this.generateNewText();
  }

  onUserInput(event: any) {
    this.userText = event.target.value;
  }

  // Random text generator (example)
  generateNewText() {
    this.generatedText = this.lorem.generateSentences(5); // or however you're generating
    this.userText = '';
    this.reportVisible = false;

    //Stop any running timer
    this.timerRunning = false;
    this.timeLeft = 0;
    clearInterval(this.timerInterval);
  }

  startChallenge(seconds: number) {
    this.generateNewText();
    this.timeLeft = seconds;
    this.timerRunning = true;
    this.userText = '';
    this.correctChars = 0;
    this.accuracy = 0;
    this.reportVisible = false;

    // Start countdown
    clearInterval(this.timerInterval);
    this.timerInterval = setInterval(() => {
      if (this.timeLeft > 0) {
        this.timeLeft--;
      } else {
        this.timerRunning = false;
        clearInterval(this.timerInterval);
        this.calculateAccuracy();
      }
    }, 1000);
  }

  calculateAccuracy() {
    let correct = 0;
    const total = this.userText.length;
    for (let i = 0; i < total; i++) {
      if (this.userText[i] === this.generatedText[i]) {
        correct++;
      }
    }
    this.correctChars = correct;
    this.accuracy = total > 0 ? Math.round((correct / total) * 100) : 0;
    this.reportVisible = true;

    // Update chart
    this.barChartData.datasets[0].data = [total, correct, this.accuracy];
  }
}
