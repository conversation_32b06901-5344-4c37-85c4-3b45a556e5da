{"version": 3, "sources": ["../../../../../../node_modules/lorem-ipsum/dist/constants/formats.js", "../../../../../../node_modules/lorem-ipsum/dist/constants/units.js", "../../../../../../node_modules/lorem-ipsum/dist/constants/words.js", "../../../../../../node_modules/lorem-ipsum/dist/constants/lineEndings.js", "../../../../../../node_modules/lorem-ipsum/dist/util/capitalize.js", "../../../../../../node_modules/lorem-ipsum/dist/util/isNode.js", "../../../../../../node_modules/lorem-ipsum/dist/util/isReactNative.js", "../../../../../../node_modules/lorem-ipsum/dist/constants/platforms.js", "../../../../../../node_modules/lorem-ipsum/dist/util/isWindows.js", "../../../../../../node_modules/lorem-ipsum/dist/util/makeArrayOfLength.js", "../../../../../../node_modules/lorem-ipsum/dist/util/makeArrayOfStrings.js", "../../../../../../node_modules/lorem-ipsum/dist/util/index.js", "../../../../../../node_modules/lorem-ipsum/dist/lib/generator.js", "../../../../../../node_modules/lorem-ipsum/dist/lib/LoremIpsum.js", "../../../../../../node_modules/lorem-ipsum/dist/index.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.FORMAT_PLAIN = exports.FORMAT_HTML = exports.FORMATS = void 0;\nvar FORMAT_HTML = \"html\";\nexports.FORMAT_HTML = FORMAT_HTML;\nvar FORMAT_PLAIN = \"plain\";\nexports.FORMAT_PLAIN = FORMAT_PLAIN;\nvar FORMATS = [FORMAT_HTML, FORMAT_PLAIN];\nexports.FORMATS = FORMATS;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UNIT_WORDS = exports.UNIT_WORD = exports.UNIT_SENTENCES = exports.UNIT_SENTENCE = exports.UNIT_PARAGRAPHS = exports.UNIT_PARAGRAPH = exports.UNITS = void 0;\nvar UNIT_WORDS = \"words\";\nexports.UNIT_WORDS = UNIT_WORDS;\nvar UNIT_WORD = \"word\";\nexports.UNIT_WORD = UNIT_WORD;\nvar UNIT_SENTENCES = \"sentences\";\nexports.UNIT_SENTENCES = UNIT_SENTENCES;\nvar UNIT_SENTENCE = \"sentence\";\nexports.UNIT_SENTENCE = UNIT_SENTENCE;\nvar UNIT_PARAGRAPHS = \"paragraphs\";\nexports.UNIT_PARAGRAPHS = UNIT_PARAGRAPHS;\nvar UNIT_PARAGRAPH = \"paragraph\";\nexports.UNIT_PARAGRAPH = UNIT_PARAGRAPH;\nvar UNITS = [UNIT_WORDS, UNIT_WORD, UNIT_SENTENCES, UNIT_SENTENCE, UNIT_PARAGRAPHS, UNIT_PARAGRAPH];\nexports.UNITS = UNITS;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.WORDS = void 0;\nvar WORDS = [\"ad\", \"adipisicing\", \"aliqua\", \"aliquip\", \"amet\", \"anim\", \"aute\", \"cillum\", \"commodo\", \"consectetur\", \"consequat\", \"culpa\", \"cupidatat\", \"deserunt\", \"do\", \"dolor\", \"dolore\", \"duis\", \"ea\", \"eiusmod\", \"elit\", \"enim\", \"esse\", \"est\", \"et\", \"eu\", \"ex\", \"excepteur\", \"exercitation\", \"fugiat\", \"id\", \"in\", \"incididunt\", \"ipsum\", \"irure\", \"labore\", \"laboris\", \"laborum\", \"Lorem\", \"magna\", \"minim\", \"mollit\", \"nisi\", \"non\", \"nostrud\", \"nulla\", \"occaecat\", \"officia\", \"pariatur\", \"proident\", \"qui\", \"quis\", \"reprehenderit\", \"sint\", \"sit\", \"sunt\", \"tempor\", \"ullamco\", \"ut\", \"velit\", \"veniam\", \"voluptate\"];\nexports.WORDS = WORDS;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.LINE_ENDINGS = void 0;\nvar LINE_ENDINGS = {\n  POSIX: \"\\n\",\n  WIN32: \"\\r\\n\"\n};\nexports.LINE_ENDINGS = LINE_ENDINGS;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n/**\r\n * @param str  A string that may or may not be capitalized.\r\n * @returns    A capitalized string.\r\n */\nvar capitalize = function capitalize(str) {\n  var trimmed = str.trim();\n  return trimmed.charAt(0).toUpperCase() + trimmed.slice(1);\n};\n\nvar _default = capitalize;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n/**\r\n * @returns  True if the runtime is NodeJS.\r\n */\nvar isNode = function isNode() {\n  return typeof module !== \"undefined\" && !!module.exports;\n};\n\nvar _default = isNode;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n/**\r\n * Check if runtime is ReactNative.\r\n * Solution based on https://github.com/knicklabs/lorem-ipsum.js/pull/52/files\r\n *\r\n * @returns  True if runtime is ReactNative.\r\n */\nvar isReactNative = function isReactNative() {\n  var isReactNativeResult = false;\n\n  try {\n    isReactNativeResult = navigator.product === \"ReactNative\";\n  } catch (e) {\n    isReactNativeResult = false;\n  }\n\n  return isReactNativeResult;\n};\n\nvar _default = isReactNative;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.SUPPORTED_PLATFORMS = void 0;\nvar SUPPORTED_PLATFORMS = {\n  DARWIN: \"darwin\",\n  LINUX: \"linux\",\n  WIN32: \"win32\"\n};\nexports.SUPPORTED_PLATFORMS = SUPPORTED_PLATFORMS;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _platforms = require(\"../constants/platforms\");\n\n/**\r\n * @returns True if process is windows.\r\n */\nvar isWindows = function isWindows() {\n  var isWindowsResult = false;\n\n  try {\n    isWindowsResult = process.platform === _platforms.SUPPORTED_PLATFORMS.WIN32;\n  } catch (e) {\n    isWindowsResult = false;\n  }\n\n  return isWindowsResult;\n};\n\nvar _default = isWindows;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\n/**\r\n * @param length Length \"x\".\r\n * @returns      An array of indexes of length \"x\".\r\n */\nvar makeArrayOfLength = function makeArrayOfLength() {\n  var length = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  return Array.apply(null, Array(length)).map(function (item, index) {\n    return index;\n  });\n};\n\nvar _default = makeArrayOfLength;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _makeArrayOfLength = _interopRequireDefault(require(\"./makeArrayOfLength\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\n/**\r\n * @param length  Length \"x\".\r\n * @returns       An array of strings of length \"x\".\r\n */\nvar makeArrayOfStrings = function makeArrayOfStrings(length, makeString) {\n  var arr = (0, _makeArrayOfLength[\"default\"])(length);\n  return arr.map(function () {\n    return makeString();\n  });\n};\n\nvar _default = makeArrayOfStrings;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"capitalize\", {\n  enumerable: true,\n  get: function get() {\n    return _capitalize[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"isNode\", {\n  enumerable: true,\n  get: function get() {\n    return _isNode[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"isReactNative\", {\n  enumerable: true,\n  get: function get() {\n    return _isReactNative[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"isWindows\", {\n  enumerable: true,\n  get: function get() {\n    return _isWindows[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"makeArrayOfLength\", {\n  enumerable: true,\n  get: function get() {\n    return _makeArrayOfLength[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"makeArrayOfStrings\", {\n  enumerable: true,\n  get: function get() {\n    return _makeArrayOfStrings[\"default\"];\n  }\n});\n\nvar _capitalize = _interopRequireDefault(require(\"./capitalize\"));\n\nvar _isNode = _interopRequireDefault(require(\"./isNode\"));\n\nvar _isReactNative = _interopRequireDefault(require(\"./isReactNative\"));\n\nvar _isWindows = _interopRequireDefault(require(\"./isWindows\"));\n\nvar _makeArrayOfLength = _interopRequireDefault(require(\"./makeArrayOfLength\"));\n\nvar _makeArrayOfStrings = _interopRequireDefault(require(\"./makeArrayOfStrings\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _words = require(\"../constants/words\");\n\nvar _util = require(\"../util\");\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar Generator = /*#__PURE__*/function () {\n  function Generator() {\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n        _ref$sentencesPerPara = _ref.sentencesPerParagraph,\n        sentencesPerParagraph = _ref$sentencesPerPara === void 0 ? {\n      max: 7,\n      min: 3\n    } : _ref$sentencesPerPara,\n        _ref$wordsPerSentence = _ref.wordsPerSentence,\n        wordsPerSentence = _ref$wordsPerSentence === void 0 ? {\n      max: 15,\n      min: 5\n    } : _ref$wordsPerSentence,\n        random = _ref.random,\n        seed = _ref.seed,\n        _ref$words = _ref.words,\n        words = _ref$words === void 0 ? _words.WORDS : _ref$words;\n\n    _classCallCheck(this, Generator);\n\n    _defineProperty(this, \"sentencesPerParagraph\", void 0);\n\n    _defineProperty(this, \"wordsPerSentence\", void 0);\n\n    _defineProperty(this, \"random\", void 0);\n\n    _defineProperty(this, \"words\", void 0);\n\n    if (sentencesPerParagraph.min > sentencesPerParagraph.max) {\n      throw new Error(\"Minimum number of sentences per paragraph (\".concat(sentencesPerParagraph.min, \") cannot exceed maximum (\").concat(sentencesPerParagraph.max, \").\"));\n    }\n\n    if (wordsPerSentence.min > wordsPerSentence.max) {\n      throw new Error(\"Minimum number of words per sentence (\".concat(wordsPerSentence.min, \") cannot exceed maximum (\").concat(wordsPerSentence.max, \").\"));\n    }\n\n    this.sentencesPerParagraph = sentencesPerParagraph;\n    this.words = words;\n    this.wordsPerSentence = wordsPerSentence;\n    this.random = random || Math.random;\n  }\n\n  _createClass(Generator, [{\n    key: \"generateRandomInteger\",\n    value: function generateRandomInteger(min, max) {\n      return Math.floor(this.random() * (max - min + 1) + min);\n    }\n  }, {\n    key: \"generateRandomWords\",\n    value: function generateRandomWords(num) {\n      var _this = this;\n\n      var _this$wordsPerSentenc = this.wordsPerSentence,\n          min = _this$wordsPerSentenc.min,\n          max = _this$wordsPerSentenc.max;\n      var length = num || this.generateRandomInteger(min, max);\n      return (0, _util.makeArrayOfLength)(length).reduce(function (accumulator, index) {\n        return \"\".concat(_this.pluckRandomWord(), \" \").concat(accumulator);\n      }, \"\").trim();\n    }\n  }, {\n    key: \"generateRandomSentence\",\n    value: function generateRandomSentence(num) {\n      return \"\".concat((0, _util.capitalize)(this.generateRandomWords(num)), \".\");\n    }\n  }, {\n    key: \"generateRandomParagraph\",\n    value: function generateRandomParagraph(num) {\n      var _this2 = this;\n\n      var _this$sentencesPerPar = this.sentencesPerParagraph,\n          min = _this$sentencesPerPar.min,\n          max = _this$sentencesPerPar.max;\n      var length = num || this.generateRandomInteger(min, max);\n      return (0, _util.makeArrayOfLength)(length).reduce(function (accumulator, index) {\n        return \"\".concat(_this2.generateRandomSentence(), \" \").concat(accumulator);\n      }, \"\").trim();\n    }\n  }, {\n    key: \"pluckRandomWord\",\n    value: function pluckRandomWord() {\n      var min = 0;\n      var max = this.words.length - 1;\n      var index = this.generateRandomInteger(min, max);\n      return this.words[index];\n    }\n  }]);\n\n  return Generator;\n}();\n\nvar _default = Generator;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n\nvar _formats = require(\"../constants/formats\");\n\nvar _lineEndings = require(\"../constants/lineEndings\");\n\nvar _generator = _interopRequireDefault(require(\"../lib/generator\"));\n\nvar _util = require(\"../util\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nvar LoremIpsum = /*#__PURE__*/function () {\n  function LoremIpsum() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : _formats.FORMAT_PLAIN;\n    var suffix = arguments.length > 2 ? arguments[2] : undefined;\n\n    _classCallCheck(this, LoremIpsum);\n\n    this.format = format;\n    this.suffix = suffix;\n\n    _defineProperty(this, \"generator\", void 0);\n\n    if (_formats.FORMATS.indexOf(format.toLowerCase()) === -1) {\n      throw new Error(\"\".concat(format, \" is an invalid format. Please use \").concat(_formats.FORMATS.join(\" or \"), \".\"));\n    }\n\n    this.generator = new _generator[\"default\"](options);\n  }\n\n  _createClass(LoremIpsum, [{\n    key: \"getLineEnding\",\n    value: function getLineEnding() {\n      if (this.suffix) {\n        return this.suffix;\n      }\n\n      if (!(0, _util.isReactNative)() && (0, _util.isNode)() && (0, _util.isWindows)()) {\n        return _lineEndings.LINE_ENDINGS.WIN32;\n      }\n\n      return _lineEndings.LINE_ENDINGS.POSIX;\n    }\n  }, {\n    key: \"formatString\",\n    value: function formatString(str) {\n      if (this.format === _formats.FORMAT_HTML) {\n        return \"<p>\".concat(str, \"</p>\");\n      }\n\n      return str;\n    }\n  }, {\n    key: \"formatStrings\",\n    value: function formatStrings(strings) {\n      var _this = this;\n\n      return strings.map(function (str) {\n        return _this.formatString(str);\n      });\n    }\n  }, {\n    key: \"generateWords\",\n    value: function generateWords(num) {\n      return this.formatString(this.generator.generateRandomWords(num));\n    }\n  }, {\n    key: \"generateSentences\",\n    value: function generateSentences(num) {\n      return this.formatString(this.generator.generateRandomParagraph(num));\n    }\n  }, {\n    key: \"generateParagraphs\",\n    value: function generateParagraphs(num) {\n      var makeString = this.generator.generateRandomParagraph.bind(this.generator);\n      return this.formatStrings((0, _util.makeArrayOfStrings)(num, makeString)).join(this.getLineEnding());\n    }\n  }]);\n\n  return LoremIpsum;\n}();\n\nvar _default = LoremIpsum;\nexports[\"default\"] = _default;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"LoremIpsum\", {\n  enumerable: true,\n  get: function get() {\n    return _LoremIpsum[\"default\"];\n  }\n});\nexports.loremIpsum = void 0;\n\nvar _formats = require(\"./constants/formats\");\n\nvar _units = require(\"./constants/units\");\n\nvar _words = require(\"./constants/words\");\n\nvar _LoremIpsum = _interopRequireDefault(require(\"./lib/LoremIpsum\"));\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\n\nvar loremIpsum = function loremIpsum() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n      _ref$count = _ref.count,\n      count = _ref$count === void 0 ? 1 : _ref$count,\n      _ref$format = _ref.format,\n      format = _ref$format === void 0 ? _formats.FORMAT_PLAIN : _ref$format,\n      _ref$paragraphLowerBo = _ref.paragraphLowerBound,\n      paragraphLowerBound = _ref$paragraphLowerBo === void 0 ? 3 : _ref$paragraphLowerBo,\n      _ref$paragraphUpperBo = _ref.paragraphUpperBound,\n      paragraphUpperBound = _ref$paragraphUpperBo === void 0 ? 7 : _ref$paragraphUpperBo,\n      random = _ref.random,\n      _ref$sentenceLowerBou = _ref.sentenceLowerBound,\n      sentenceLowerBound = _ref$sentenceLowerBou === void 0 ? 5 : _ref$sentenceLowerBou,\n      _ref$sentenceUpperBou = _ref.sentenceUpperBound,\n      sentenceUpperBound = _ref$sentenceUpperBou === void 0 ? 15 : _ref$sentenceUpperBou,\n      _ref$units = _ref.units,\n      units = _ref$units === void 0 ? _units.UNIT_SENTENCES : _ref$units,\n      _ref$words = _ref.words,\n      words = _ref$words === void 0 ? _words.WORDS : _ref$words,\n      _ref$suffix = _ref.suffix,\n      suffix = _ref$suffix === void 0 ? \"\" : _ref$suffix;\n\n  var options = {\n    random: random,\n    sentencesPerParagraph: {\n      max: paragraphUpperBound,\n      min: paragraphLowerBound\n    },\n    words: words,\n    wordsPerSentence: {\n      max: sentenceUpperBound,\n      min: sentenceLowerBound\n    }\n  };\n  var lorem = new _LoremIpsum[\"default\"](options, format, suffix);\n\n  switch (units) {\n    case _units.UNIT_PARAGRAPHS:\n    case _units.UNIT_PARAGRAPH:\n      return lorem.generateParagraphs(count);\n\n    case _units.UNIT_SENTENCES:\n    case _units.UNIT_SENTENCE:\n      return lorem.generateSentences(count);\n\n    case _units.UNIT_WORDS:\n    case _units.UNIT_WORD:\n      return lorem.generateWords(count);\n\n    default:\n      return \"\";\n  }\n};\n\nexports.loremIpsum = loremIpsum;\n"], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe,QAAQ,cAAc,QAAQ,UAAU;AAC/D,QAAI,cAAc;AAClB,YAAQ,cAAc;AACtB,QAAI,eAAe;AACnB,YAAQ,eAAe;AACvB,QAAI,UAAU,CAAC,aAAa,YAAY;AACxC,YAAQ,UAAU;AAAA;AAAA;;;ACXlB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,aAAa,QAAQ,YAAY,QAAQ,iBAAiB,QAAQ,gBAAgB,QAAQ,kBAAkB,QAAQ,iBAAiB,QAAQ,QAAQ;AAC7J,QAAI,aAAa;AACjB,YAAQ,aAAa;AACrB,QAAI,YAAY;AAChB,YAAQ,YAAY;AACpB,QAAI,iBAAiB;AACrB,YAAQ,iBAAiB;AACzB,QAAI,gBAAgB;AACpB,YAAQ,gBAAgB;AACxB,QAAI,kBAAkB;AACtB,YAAQ,kBAAkB;AAC1B,QAAI,iBAAiB;AACrB,YAAQ,iBAAiB;AACzB,QAAI,QAAQ,CAAC,YAAY,WAAW,gBAAgB,eAAe,iBAAiB,cAAc;AAClG,YAAQ,QAAQ;AAAA;AAAA;;;ACnBhB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,QAAQ;AAChB,QAAI,QAAQ,CAAC,MAAM,eAAe,UAAU,WAAW,QAAQ,QAAQ,QAAQ,UAAU,WAAW,eAAe,aAAa,SAAS,aAAa,YAAY,MAAM,SAAS,UAAU,QAAQ,MAAM,WAAW,QAAQ,QAAQ,QAAQ,OAAO,MAAM,MAAM,MAAM,aAAa,gBAAgB,UAAU,MAAM,MAAM,cAAc,SAAS,SAAS,UAAU,WAAW,WAAW,SAAS,SAAS,SAAS,UAAU,QAAQ,OAAO,WAAW,SAAS,YAAY,WAAW,YAAY,YAAY,OAAO,QAAQ,iBAAiB,QAAQ,OAAO,QAAQ,UAAU,WAAW,MAAM,SAAS,UAAU,WAAW;AAC/lB,YAAQ,QAAQ;AAAA;AAAA;;;ACPhB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,QAAI,eAAe;AAAA,MACjB,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,YAAQ,eAAe;AAAA;AAAA;;;ACVvB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAMrB,QAAI,aAAa,SAASA,YAAW,KAAK;AACxC,UAAI,UAAU,IAAI,KAAK;AACvB,aAAO,QAAQ,OAAO,CAAC,EAAE,YAAY,IAAI,QAAQ,MAAM,CAAC;AAAA,IAC1D;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACjBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAKrB,QAAI,SAAS,SAASC,UAAS;AAC7B,aAAO,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO;AAAA,IACnD;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACfrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAQrB,QAAI,gBAAgB,SAASC,iBAAgB;AAC3C,UAAI,sBAAsB;AAE1B,UAAI;AACF,8BAAsB,UAAU,YAAY;AAAA,MAC9C,SAAS,GAAG;AACV,8BAAsB;AAAA,MACxB;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;AC1BrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,sBAAsB;AAC9B,QAAI,sBAAsB;AAAA,MACxB,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT;AACA,YAAQ,sBAAsB;AAAA;AAAA;;;ACX9B;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAErB,QAAI,aAAa;AAKjB,QAAI,YAAY,SAASC,aAAY;AACnC,UAAI,kBAAkB;AAEtB,UAAI;AACF,0BAAkB,QAAQ,aAAa,WAAW,oBAAoB;AAAA,MACxE,SAAS,GAAG;AACV,0BAAkB;AAAA,MACpB;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACzBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAMrB,QAAI,oBAAoB,SAASC,qBAAoB;AACnD,UAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACjF,aAAO,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC,EAAE,IAAI,SAAU,MAAM,OAAO;AACjE,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACnBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAErB,QAAI,qBAAqB,uBAAuB,2BAA8B;AAE9E,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAMhG,QAAI,qBAAqB,SAASC,oBAAmB,QAAQ,YAAY;AACvE,UAAI,OAAO,GAAG,mBAAmB,SAAS,GAAG,MAAM;AACnD,aAAO,IAAI,IAAI,WAAY;AACzB,eAAO,WAAW;AAAA,MACpB,CAAC;AAAA,IACH;AAEA,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;ACvBrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,YAAY,SAAS;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,UAAU;AAAA,MACvC,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,QAAQ,SAAS;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,eAAe,SAAS;AAAA,MACjC;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,aAAa;AAAA,MAC1C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,WAAW,SAAS;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,qBAAqB;AAAA,MAClD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,mBAAmB,SAAS;AAAA,MACrC;AAAA,IACF,CAAC;AACD,WAAO,eAAe,SAAS,sBAAsB;AAAA,MACnD,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,oBAAoB,SAAS;AAAA,MACtC;AAAA,IACF,CAAC;AAED,QAAI,cAAc,uBAAuB,oBAAuB;AAEhE,QAAI,UAAU,uBAAuB,gBAAmB;AAExD,QAAI,iBAAiB,uBAAuB,uBAA0B;AAEtE,QAAI,aAAa,uBAAuB,mBAAsB;AAE9D,QAAI,qBAAqB,uBAAuB,2BAA8B;AAE9E,QAAI,sBAAsB,uBAAuB,4BAA+B;AAEhF,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAAA;AAAA;;;ACtDhG;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAErB,QAAI,SAAS;AAEb,QAAI,QAAQ;AAEZ,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MAAG;AAAA,IAAE;AAE5T,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,aAAO;AAAA,IAAa;AAE5R,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAEhN,QAAI,YAAyB,WAAY;AACvC,eAASC,aAAY;AACnB,YAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,wBAAwB,KAAK,uBAC7B,wBAAwB,0BAA0B,SAAS;AAAA,UAC7D,KAAK;AAAA,UACL,KAAK;AAAA,QACP,IAAI,uBACA,wBAAwB,KAAK,kBAC7B,mBAAmB,0BAA0B,SAAS;AAAA,UACxD,KAAK;AAAA,UACL,KAAK;AAAA,QACP,IAAI,uBACA,SAAS,KAAK,QACd,OAAO,KAAK,MACZ,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,OAAO,QAAQ;AAEnD,wBAAgB,MAAMA,UAAS;AAE/B,wBAAgB,MAAM,yBAAyB,MAAM;AAErD,wBAAgB,MAAM,oBAAoB,MAAM;AAEhD,wBAAgB,MAAM,UAAU,MAAM;AAEtC,wBAAgB,MAAM,SAAS,MAAM;AAErC,YAAI,sBAAsB,MAAM,sBAAsB,KAAK;AACzD,gBAAM,IAAI,MAAM,8CAA8C,OAAO,sBAAsB,KAAK,2BAA2B,EAAE,OAAO,sBAAsB,KAAK,IAAI,CAAC;AAAA,QACtK;AAEA,YAAI,iBAAiB,MAAM,iBAAiB,KAAK;AAC/C,gBAAM,IAAI,MAAM,yCAAyC,OAAO,iBAAiB,KAAK,2BAA2B,EAAE,OAAO,iBAAiB,KAAK,IAAI,CAAC;AAAA,QACvJ;AAEA,aAAK,wBAAwB;AAC7B,aAAK,QAAQ;AACb,aAAK,mBAAmB;AACxB,aAAK,SAAS,UAAU,KAAK;AAAA,MAC/B;AAEA,mBAAaA,YAAW,CAAC;AAAA,QACvB,KAAK;AAAA,QACL,OAAO,SAAS,sBAAsB,KAAK,KAAK;AAC9C,iBAAO,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,KAAK,GAAG;AAAA,QACzD;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,oBAAoB,KAAK;AACvC,cAAI,QAAQ;AAEZ,cAAI,wBAAwB,KAAK,kBAC7B,MAAM,sBAAsB,KAC5B,MAAM,sBAAsB;AAChC,cAAI,SAAS,OAAO,KAAK,sBAAsB,KAAK,GAAG;AACvD,kBAAQ,GAAG,MAAM,mBAAmB,MAAM,EAAE,OAAO,SAAU,aAAa,OAAO;AAC/E,mBAAO,GAAG,OAAO,MAAM,gBAAgB,GAAG,GAAG,EAAE,OAAO,WAAW;AAAA,UACnE,GAAG,EAAE,EAAE,KAAK;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,uBAAuB,KAAK;AAC1C,iBAAO,GAAG,QAAQ,GAAG,MAAM,YAAY,KAAK,oBAAoB,GAAG,CAAC,GAAG,GAAG;AAAA,QAC5E;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,wBAAwB,KAAK;AAC3C,cAAI,SAAS;AAEb,cAAI,wBAAwB,KAAK,uBAC7B,MAAM,sBAAsB,KAC5B,MAAM,sBAAsB;AAChC,cAAI,SAAS,OAAO,KAAK,sBAAsB,KAAK,GAAG;AACvD,kBAAQ,GAAG,MAAM,mBAAmB,MAAM,EAAE,OAAO,SAAU,aAAa,OAAO;AAC/E,mBAAO,GAAG,OAAO,OAAO,uBAAuB,GAAG,GAAG,EAAE,OAAO,WAAW;AAAA,UAC3E,GAAG,EAAE,EAAE,KAAK;AAAA,QACd;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB;AAChC,cAAI,MAAM;AACV,cAAI,MAAM,KAAK,MAAM,SAAS;AAC9B,cAAI,QAAQ,KAAK,sBAAsB,KAAK,GAAG;AAC/C,iBAAO,KAAK,MAAM,KAAK;AAAA,QACzB;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE;AAEF,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;AC/GrB;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,SAAS,IAAI;AAErB,QAAI,WAAW;AAEf,QAAI,eAAe;AAEnB,QAAI,aAAa,uBAAuB,mBAA2B;AAEnE,QAAI,QAAQ;AAEZ,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAEhG,aAAS,gBAAgB,UAAU,aAAa;AAAE,UAAI,EAAE,oBAAoB,cAAc;AAAE,cAAM,IAAI,UAAU,mCAAmC;AAAA,MAAG;AAAA,IAAE;AAExJ,aAAS,kBAAkB,QAAQ,OAAO;AAAE,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,YAAI,aAAa,MAAM,CAAC;AAAG,mBAAW,aAAa,WAAW,cAAc;AAAO,mBAAW,eAAe;AAAM,YAAI,WAAW,WAAY,YAAW,WAAW;AAAM,eAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,MAAG;AAAA,IAAE;AAE5T,aAAS,aAAa,aAAa,YAAY,aAAa;AAAE,UAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,UAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,aAAO,eAAe,aAAa,aAAa,EAAE,UAAU,MAAM,CAAC;AAAG,aAAO;AAAA,IAAa;AAE5R,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAEhN,QAAI,aAA0B,WAAY;AACxC,eAASC,cAAa;AACpB,YAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,YAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,SAAS;AAC1F,YAAI,SAAS,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAEnD,wBAAgB,MAAMA,WAAU;AAEhC,aAAK,SAAS;AACd,aAAK,SAAS;AAEd,wBAAgB,MAAM,aAAa,MAAM;AAEzC,YAAI,SAAS,QAAQ,QAAQ,OAAO,YAAY,CAAC,MAAM,IAAI;AACzD,gBAAM,IAAI,MAAM,GAAG,OAAO,QAAQ,oCAAoC,EAAE,OAAO,SAAS,QAAQ,KAAK,MAAM,GAAG,GAAG,CAAC;AAAA,QACpH;AAEA,aAAK,YAAY,IAAI,WAAW,SAAS,EAAE,OAAO;AAAA,MACpD;AAEA,mBAAaA,aAAY,CAAC;AAAA,QACxB,KAAK;AAAA,QACL,OAAO,SAAS,gBAAgB;AAC9B,cAAI,KAAK,QAAQ;AACf,mBAAO,KAAK;AAAA,UACd;AAEA,cAAI,EAAE,GAAG,MAAM,eAAe,MAAM,GAAG,MAAM,QAAQ,MAAM,GAAG,MAAM,WAAW,GAAG;AAChF,mBAAO,aAAa,aAAa;AAAA,UACnC;AAEA,iBAAO,aAAa,aAAa;AAAA,QACnC;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,aAAa,KAAK;AAChC,cAAI,KAAK,WAAW,SAAS,aAAa;AACxC,mBAAO,MAAM,OAAO,KAAK,MAAM;AAAA,UACjC;AAEA,iBAAO;AAAA,QACT;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,SAAS;AACrC,cAAI,QAAQ;AAEZ,iBAAO,QAAQ,IAAI,SAAU,KAAK;AAChC,mBAAO,MAAM,aAAa,GAAG;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,cAAc,KAAK;AACjC,iBAAO,KAAK,aAAa,KAAK,UAAU,oBAAoB,GAAG,CAAC;AAAA,QAClE;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,kBAAkB,KAAK;AACrC,iBAAO,KAAK,aAAa,KAAK,UAAU,wBAAwB,GAAG,CAAC;AAAA,QACtE;AAAA,MACF,GAAG;AAAA,QACD,KAAK;AAAA,QACL,OAAO,SAAS,mBAAmB,KAAK;AACtC,cAAI,aAAa,KAAK,UAAU,wBAAwB,KAAK,KAAK,SAAS;AAC3E,iBAAO,KAAK,eAAe,GAAG,MAAM,oBAAoB,KAAK,UAAU,CAAC,EAAE,KAAK,KAAK,cAAc,CAAC;AAAA,QACrG;AAAA,MACF,CAAC,CAAC;AAEF,aAAOA;AAAA,IACT,EAAE;AAEF,QAAI,WAAW;AACf,YAAQ,SAAS,IAAI;AAAA;AAAA;;;AClGrB;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,YAAY;AAAA,MACZ,KAAK,SAAS,MAAM;AAClB,eAAO,YAAY,SAAS;AAAA,MAC9B;AAAA,IACF,CAAC;AACD,YAAQ,aAAa;AAErB,QAAI,WAAW;AAEf,QAAI,SAAS;AAEb,QAAI,SAAS;AAEb,QAAI,cAAc,uBAAuB,oBAA2B;AAEpE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,WAAW,IAAI;AAAA,IAAG;AAEhG,QAAI,aAAa,SAASC,cAAa;AACrC,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,GAC5E,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,IAAI,YACpC,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,SAAS,eAAe,aAC1D,wBAAwB,KAAK,qBAC7B,sBAAsB,0BAA0B,SAAS,IAAI,uBAC7D,wBAAwB,KAAK,qBAC7B,sBAAsB,0BAA0B,SAAS,IAAI,uBAC7D,SAAS,KAAK,QACd,wBAAwB,KAAK,oBAC7B,qBAAqB,0BAA0B,SAAS,IAAI,uBAC5D,wBAAwB,KAAK,oBAC7B,qBAAqB,0BAA0B,SAAS,KAAK,uBAC7D,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,OAAO,iBAAiB,YACxD,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,OAAO,QAAQ,YAC/C,cAAc,KAAK,QACnB,SAAS,gBAAgB,SAAS,KAAK;AAE3C,UAAI,UAAU;AAAA,QACZ;AAAA,QACA,uBAAuB;AAAA,UACrB,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,QACA;AAAA,QACA,kBAAkB;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,UAAI,QAAQ,IAAI,YAAY,SAAS,EAAE,SAAS,QAAQ,MAAM;AAE9D,cAAQ,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,QACZ,KAAK,OAAO;AACV,iBAAO,MAAM,mBAAmB,KAAK;AAAA,QAEvC,KAAK,OAAO;AAAA,QACZ,KAAK,OAAO;AACV,iBAAO,MAAM,kBAAkB,KAAK;AAAA,QAEtC,KAAK,OAAO;AAAA,QACZ,KAAK,OAAO;AACV,iBAAO,MAAM,cAAc,KAAK;AAAA,QAElC;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAEA,YAAQ,aAAa;AAAA;AAAA;", "names": ["capitalize", "isNode", "isReactNative", "isWindows", "makeArrayOfLength", "makeArrayOfStrings", "Generator", "LoremIpsum", "loremIpsum"]}